import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Avatar } from "@/components/ui/avatar";
import { Co<PERSON>, UserIcon, Bo<PERSON> } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useTypingEffect } from "@/hooks/use-typing-effect";
import { useActiveAccount } from "thirdweb/react";
import { getExplorerUrl } from "@/lib/chainConfig";

interface ChatMessageProps {
  message: {
    id: number;
    role: string;
    content: string;
    timestamp: string;
    metadata?: {
      chainId?: string;
      source?: string;
      executionTime?: number;
      blockchainData?: any;
      isStreaming?: boolean;
    };
  };
}

const ChatMessage = ({ message }: ChatMessageProps) => {
  const { toast } = useToast();
  const isUserMessage = message.role === "user";
  const activeAccount = useActiveAccount();
  const [transactionHash, setTransactionHash] = useState<string | null>(null);

  // Check if this is a streaming message
  const isStreaming = message.metadata?.isStreaming;

  // Check if this message was ever streaming (to prevent typing effect after streaming completes)
  const wasStreaming = message.metadata?.isStreaming !== undefined;

  // Add typing effect for assistant messages (only if not streaming and never was streaming)
  const { displayedText, isTyping, showFullText } = useTypingEffect(
    !isUserMessage && !isStreaming && !wasStreaming ? message.content : "",
    7 // twice as fast typing speed (was 15ms, now 7ms)
  );

  // For streaming messages or messages that were streaming, show content directly
  const contentToShow =
    isStreaming || isUserMessage || wasStreaming
      ? message.content
      : displayedText;

  // Execute transaction function using Web3 browser API
  const executeTransaction = async (txData: any) => {
    if (!window.ethereum) {
      toast({
        title: "No Wallet Found",
        description: "Please install MetaMask or another Web3 wallet",
        variant: "destructive",
      });
      return;
    }

    try {
      // Request wallet connection if needed
      const accounts = await window.ethereum.request({
        method: "eth_requestAccounts",
      });

      if (!accounts || accounts.length === 0) {
        toast({
          title: "Wallet Not Connected",
          description: "Please connect your wallet to execute transactions",
          variant: "destructive",
        });
        return;
      }

      toast({
        title: "Transaction Pending",
        description: "Please confirm the transaction in your wallet...",
      });

      // Send transaction using the browser wallet
      const txHash = await window.ethereum.request({
        method: "eth_sendTransaction",
        params: [
          {
            from: accounts[0],
            to: txData.to,
            value: txData.value,
            data: txData.data || "0x",
          },
        ],
      });

      // Save the transaction hash for the explorer link
      setTransactionHash(txHash);

      toast({
        title: "Transaction Successful!",
        description: `Transaction hash: ${txHash}`,
      });
    } catch (error: any) {
      console.error("Transaction failed:", error);
      toast({
        title: "Transaction Failed",
        description: error.message || "Transaction was rejected or failed",
        variant: "destructive",
      });
    }
  };

  // Format timestamp
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return `${diffInSeconds}s ago`;
    } else if (diffInSeconds < 3600) {
      return `${Math.floor(diffInSeconds / 60)}m ago`;
    } else if (diffInSeconds < 86400) {
      return `${Math.floor(diffInSeconds / 3600)}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  // Parse message content with enhanced markdown formatting
  const renderContent = (content: string) => {
    // Handle empty content for streaming messages
    if (!content || typeof content !== "string") {
      if (isStreaming) {
        return (
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
            <span className="text-sm text-muted-foreground">Thinking...</span>
          </div>
        );
      }
      return <span>Loading...</span>;
    }

    // Split by code blocks first
    const parts = content.split(/```([\s\S]*?)```/);

    return parts.map((part, index) => {
      if (index % 2 === 1) {
        // Code block
        return (
          <pre
            key={index}
            className="bg-muted/50 text-foreground p-4 rounded-md border border-border my-3 overflow-x-auto font-mono"
          >
            <code className="text-foreground">{part}</code>
          </pre>
        );
      } else {
        // Regular text with enhanced formatting
        let formattedText = part;

        // Handle headers with theme colors
        formattedText = formattedText
          .replace(
            /^### (.*$)/gm,
            '<h3 class="text-lg font-semibold mt-4 mb-2 text-foreground">$1</h3>'
          )
          .replace(
            /^## (.*$)/gm,
            '<h2 class="text-xl font-semibold mt-4 mb-2 text-foreground">$1</h2>'
          )
          .replace(
            /^# (.*$)/gm,
            '<h1 class="text-2xl font-bold mt-4 mb-2 text-foreground">$1</h1>'
          );

        // Handle inline code with theme colors
        formattedText = formattedText.replace(
          /`([^`]+)`/g,
          '<code class="bg-muted/50 text-foreground px-2 py-0.5 rounded text-sm font-mono border border-border/30">$1</code>'
        );

        // Handle links with theme colors
        formattedText = formattedText.replace(
          /\[([^\]]+)\]\(([^)]+)\)/g,
          '<a href="$2" class="text-primary hover:text-primary/80 hover:underline transition-colors" target="_blank" rel="noopener noreferrer">$1</a>'
        );

        // Handle bold and italic with theme colors
        formattedText = formattedText
          .replace(
            /\*\*([^*]+)\*\*/g,
            "<strong class='font-semibold text-foreground'>$1</strong>"
          )
          .replace(
            /\*([^*]+)\*/g,
            "<em class='italic text-foreground'>$1</em>"
          );

        // Split into lines and process lists and paragraphs
        const lines = formattedText.split("\n");
        const processedLines: string[] = [];
        let inList = false;
        let listType = "";

        for (let i = 0; i < lines.length; i++) {
          const line = lines[i].trim();

          if (!line) {
            // Empty line - close any open list and add paragraph break
            if (inList) {
              processedLines.push(`</${listType}>`);
              inList = false;
              listType = "";
            }
            processedLines.push("<br>");
            continue;
          }

          // Check for list items
          const orderedMatch = line.match(/^(\d+)\.\s+(.*)$/);
          const unorderedMatch = line.match(/^[-*]\s+(.*)$/);

          if (orderedMatch) {
            // Ordered list item
            if (!inList || listType !== "ol") {
              if (inList) processedLines.push(`</${listType}>`);
              processedLines.push(
                '<ol class="list-decimal list-inside ml-4 space-y-1 text-foreground">'
              );
              inList = true;
              listType = "ol";
            }
            processedLines.push(
              `<li class="ml-2 text-foreground">${orderedMatch[2]}</li>`
            );
          } else if (unorderedMatch) {
            // Unordered list item
            if (!inList || listType !== "ul") {
              if (inList) processedLines.push(`</${listType}>`);
              processedLines.push(
                '<ul class="list-disc list-inside ml-4 space-y-1 text-foreground">'
              );
              inList = true;
              listType = "ul";
            }
            processedLines.push(
              `<li class="ml-2 text-foreground">${unorderedMatch[1]}</li>`
            );
          } else {
            // Regular line
            if (inList) {
              processedLines.push(`</${listType}>`);
              inList = false;
              listType = "";
            }

            // Check if it's a header (already processed above)
            if (line.match(/^<h[1-6]/)) {
              processedLines.push(line);
            } else {
              processedLines.push(
                `<p class="mb-2 text-foreground">${line}</p>`
              );
            }
          }
        }

        // Close any remaining open list
        if (inList) {
          processedLines.push(`</${listType}>`);
        }

        const finalHtml = processedLines.join("");

        return (
          <div
            key={index}
            dangerouslySetInnerHTML={{ __html: finalHtml }}
            className="text-foreground [&>*:first-child]:mt-0 [&>*:last-child]:mb-0"
          />
        );
      }
    });
  };

  // Copy message to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(message.content);
    toast({
      title: "Copied to clipboard",
      description: "The message has been copied to your clipboard.",
      duration: 2000,
    });
  };

  return (
    <div
      className={`fade-in px-4 md:px-8 py-4 ${
        !isUserMessage ? "bg-muted/10" : ""
      } flex`}
    >
      <Avatar
        className={`h-8 w-8 mr-4 mt-1 flex-shrink-0 ${
          isUserMessage ? "bg-muted" : "nebula-icon-bg"
        } flex items-center justify-center`}
      >
        {isUserMessage ? (
          <UserIcon className="h-4 w-4 text-muted-foreground" />
        ) : (
          <Bot className="h-4 w-4 text-foreground" />
        )}
      </Avatar>

      <div className="flex-1">
        <div className="text-foreground">
          {isUserMessage ? (
            <p>{message.content}</p>
          ) : (
            <>
              {renderContent(contentToShow)}
              {((isTyping && !wasStreaming) ||
                (isStreaming && contentToShow)) && (
                <span className="inline-block w-2 h-5 bg-primary/60 animate-pulse ml-1 align-middle"></span>
              )}
            </>
          )}
        </div>

        {/* Transaction Card - Based on Nebula UI with Real Data */}
        {!isUserMessage &&
          message.metadata?.blockchainData?.actions &&
          message.metadata.blockchainData.actions.length > 0 && (
            <div className="nebula-transaction-card">
              <div className="text-sm font-medium mb-3">
                Transaction Preview
              </div>

              {message.metadata.blockchainData.actions.map(
                (action: any, index: number) => {
                  if (action.type === "sign_transaction" && action.data) {
                    const txData = JSON.parse(action.data);
                    const chainId = txData.chainId;
                    const value = BigInt(txData.value || "0");
                    const formattedValue = (Number(value) / 1e18).toFixed(4);

                    const getChainName = (chainId: number) => {
                      switch (chainId) {
                        case 1:
                          return "Ethereum Mainnet";
                        case 137:
                          return "Polygon Mainnet";
                        case 56:
                          return "BSC Mainnet";
                        case 11155111:
                          return "Sepolia Testnet";
                        case 80002:
                          return "Amoy Testnet";
                        case 97:
                          return "BSC Testnet";
                        default:
                          return `Chain ${chainId}`;
                      }
                    };

                    const getTokenSymbol = (chainId: number) => {
                      switch (chainId) {
                        case 1:
                          return "ETH";
                        case 137:
                          return "POL";
                        case 56:
                          return "BNB";
                        case 11155111:
                          return "ETH";
                        case 80002:
                          return "POL";
                        case 97:
                          return "BNB";
                        default:
                          return "Native";
                      }
                    };

                    return (
                      <div key={index}>
                        {/* From Address */}
                        <div className="nebula-transaction-row">
                          <div className="nebula-transaction-label">From</div>
                          <div className="nebula-transaction-value flex items-center">
                            <div className="w-5 h-5 rounded-full bg-primary/20 flex items-center justify-center mr-2">
                              <UserIcon className="h-3 w-3 text-primary" />
                            </div>
                            <span>Your Wallet</span>
                          </div>
                        </div>

                        {/* To Address */}
                        <div className="nebula-transaction-row">
                          <div className="nebula-transaction-label">To</div>
                          <div className="nebula-transaction-value flex items-center">
                            <div className="w-5 h-5 rounded-full bg-secondary/20 flex items-center justify-center mr-2">
                              <UserIcon className="h-3 w-3 text-primary" />
                            </div>
                            <span>
                              {txData.to.slice(0, 6)}...{txData.to.slice(-4)}
                            </span>
                          </div>
                        </div>

                        {/* Value */}
                        <div className="nebula-transaction-row">
                          <div className="nebula-transaction-label">Value</div>
                          <div className="nebula-transaction-value">
                            {formattedValue} {getTokenSymbol(chainId)}
                          </div>
                        </div>

                        {/* Network */}
                        <div className="nebula-transaction-row">
                          <div className="nebula-transaction-label">
                            Network
                          </div>
                          <div className="nebula-transaction-value flex items-center">
                            <div
                              className={`w-3 h-3 rounded-full mr-2 ${
                                chainId === 1
                                  ? "bg-blue-500"
                                  : chainId === 137
                                  ? "bg-purple-500"
                                  : chainId === 56
                                  ? "bg-yellow-500"
                                  : "bg-gray-500"
                              }`}
                            ></div>
                            <span>{getChainName(chainId)}</span>
                          </div>
                        </div>

                        {/* Transaction Action Buttons */}
                        <div className="mt-4 pt-3 border-t border-border/50">
                          {transactionHash ? (
                            <Button
                              className="w-full bg-green-600 hover:bg-green-700 text-white theme-button"
                              onClick={async () => {
                                try {
                                  const explorerUrl = await getExplorerUrl(
                                    chainId,
                                    "tx",
                                    transactionHash
                                  );
                                  window.open(explorerUrl, "_blank");
                                } catch (error) {
                                  console.error(
                                    "Failed to get explorer URL:",
                                    error
                                  );
                                }
                              }}
                            >
                              View on Explorer ↗
                            </Button>
                          ) : (
                            <Button
                              className="w-full nebula-action-button text-primary-foreground"
                              onClick={() => executeTransaction(txData)}
                            >
                              Sign & Execute Transaction
                            </Button>
                          )}
                        </div>
                      </div>
                    );
                  }
                  return null;
                }
              )}

              {/* Gas Price Data */}
              {message.metadata.blockchainData.low && (
                <div className="mt-4 mb-2">
                  <div className="text-xs text-muted-foreground mb-2">
                    Current Gas Prices
                  </div>
                  <div className="grid grid-cols-3 gap-2">
                    <div className="bg-muted/30 p-2 rounded-md">
                      <div className="text-xs text-muted-foreground">Low</div>
                      <div className="font-medium text-sm">
                        {message.metadata.blockchainData.low} Gwei
                      </div>
                    </div>
                    <div className="bg-muted/30 p-2 rounded-md">
                      <div className="text-xs text-muted-foreground">
                        Average
                      </div>
                      <div className="font-medium text-sm">
                        {message.metadata.blockchainData.average} Gwei
                      </div>
                    </div>
                    <div className="bg-muted/30 p-2 rounded-md">
                      <div className="text-xs text-muted-foreground">High</div>
                      <div className="font-medium text-sm">
                        {message.metadata.blockchainData.high} Gwei
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

        {/* Message Actions */}
        {!isUserMessage && (
          <div className="mt-3 flex items-center text-xs text-muted-foreground">
            <Button
              variant="ghost"
              size="sm"
              className="hover:text-foreground mr-3 flex items-center gap-1 h-auto p-0"
              onClick={copyToClipboard}
              title="Copy to clipboard"
            >
              <Copy className="h-3.5 w-3.5" />
              <span>Copy</span>
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatMessage;
